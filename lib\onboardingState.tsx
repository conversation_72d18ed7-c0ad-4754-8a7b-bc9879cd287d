import { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import * as SecureStore from 'expo-secure-store';
import { surveyData } from '../components/onboarding/config';

// Storage keys for SecureStore
const STORAGE_KEY_ANSWERS = 'ONBOARDING_ANSWERS';
const STORAGE_KEY_LAST_SLIDE = 'ONBOARDING_LAST_SLIDE';
const STORAGE_KEY_COMPLETED = 'ONBOARDING_COMPLETED';

/**
 * Compute a sensible default value for a given slide configuration. Mirrors the
 * logic inside `components/onboarding/onboard.tsx` so that the UI can rely on
 * pre-populated answers even before the user interacts with a slide.
 */
function getDefaultValue(slide: any) {
  switch (slide.type) {
    case 'slider': {
      const min = typeof slide.min === 'number' ? slide.min : 0;
      const max = typeof slide.max === 'number' ? slide.max : min;
      const step = typeof slide.step === 'number' && slide.step > 0 ? slide.step : 1;
      const values: number[] = [];
      for (let v = min; v <= max; v += step) values.push(v);
      let def = typeof slide.default === 'number' ? slide.default : values[0];
      if (!values.includes(def)) {
        // eslint-disable-next-line no-console
        console.warn(`Slider default value ${def} is not a valid step for slide ${slide.id}. Falling back to ${values[0]}.`);
        def = values[0];
      }
      return def;
    }
    case 'input':
      return '';
    case 'yes-no':
      return null;
    case 'date-picker':
      return null;
    case 'image-select':
      return null;
    case 'toggle-group':
      if (Array.isArray(slide.options)) {
        return Object.fromEntries(
          slide.options.filter(Boolean).map((o: any) => [o.id, !!o.default])
        );
      }
      return {};
    case 'ranking':
      return Array.isArray(slide.options)
        ? slide.options.filter(Boolean).map((o: any) => o.id)
        : [];
    case 'multiple-choice':
      return null;
    case 'multiple-select':
      return [];
    default:
      return null;
  }
}

interface OnboardingContextValue {
  isLoaded: boolean;
  answers: Record<string, any>;
  lastSlideId: string | null;
  updateAnswer: (slideId: string, value: any) => void;
  completeOnboarding: () => Promise<void>;
  /** Convenience helper to retrieve the latest answers object. */
  getFinalAnswers: () => Record<string, any>;
}

const OnboardingStateContext = createContext<OnboardingContextValue | undefined>(undefined);

export function OnboardingStateProvider({ children }: { children: ReactNode }) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [answers, setAnswers] = useState<Record<string, any>>({});
  const [lastSlideId, setLastSlideId] = useState<string | null>(null);

  // Load persisted state on mount
  useEffect(() => {
    (async () => {
      try {
        const [storedAnswersRaw, storedLastSlide] = await Promise.all([
          SecureStore.getItemAsync(STORAGE_KEY_ANSWERS),
          SecureStore.getItemAsync(STORAGE_KEY_LAST_SLIDE),
        ]);

        let initialAnswers: Record<string, any> = {};
        if (storedAnswersRaw) {
          try {
            initialAnswers = JSON.parse(storedAnswersRaw);
          } catch (err) {
            // eslint-disable-next-line no-console
            console.warn('Failed to parse stored onboarding answers, resetting.');
          }
        }

        // Ensure that every slide has a value (in case new slides were added)
        for (const slide of surveyData) {
          if (!(slide.id in initialAnswers)) {
            initialAnswers[slide.id] = getDefaultValue(slide);
          }
        }

        setAnswers(initialAnswers);
        setLastSlideId(storedLastSlide ?? null);
      } catch (e) {
        // eslint-disable-next-line no-console
        console.error('Failed to load onboarding progress', e);
      } finally {
        setIsLoaded(true);
      }
    })();
  }, []);

  const updateAnswer = (slideId: string, value: any) => {
    setAnswers(prev => {
      const updated = { ...prev, [slideId]: value };
      void SecureStore.setItemAsync(STORAGE_KEY_ANSWERS, JSON.stringify(updated));
      void SecureStore.setItemAsync(STORAGE_KEY_LAST_SLIDE, slideId);
      return updated;
    });
    setLastSlideId(slideId);
  };

  const completeOnboarding = async () => {
    setLastSlideId(null);
    try {
      await SecureStore.setItemAsync(STORAGE_KEY_COMPLETED, 'true');
      await SecureStore.deleteItemAsync(STORAGE_KEY_LAST_SLIDE);
    } catch (err) {
      // eslint-disable-next-line no-console
      console.error('Unable to persist onboarding completion', err);
    }
  };

  const getFinalAnswers = () => answers;

  const value: OnboardingContextValue = {
    isLoaded,
    answers,
    lastSlideId,
    updateAnswer,
    completeOnboarding,
    getFinalAnswers,
  };
console.log(value)
  return (
    <OnboardingStateContext.Provider value={value}>{children}</OnboardingStateContext.Provider>
  );
}

export function useOnboardingState() {
  const context = useContext(OnboardingStateContext);
  if (!context) {
    throw new Error('useOnboardingState must be used within an OnboardingStateProvider');
  }
  return context;
} 