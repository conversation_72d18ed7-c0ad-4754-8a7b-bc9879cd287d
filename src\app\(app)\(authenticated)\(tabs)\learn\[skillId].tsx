import React, { useEffect } from 'react';
import { useLocalSearchParams } from 'expo-router';
import SkillPlayer from '@/features/skillPlayer/SkillPlayer';
import { useSkillStore } from '@/features/skillPlayer/store';
import { useUser } from '@clerk/clerk-expo';
import { useFreemium } from '@/features/freemium/FreemiumProvider';

export default function SkillPlayerScreen() {
  const { skillId } = useLocalSearchParams();
  const { user } = useUser();
  const { shouldSyncToCloud } = useFreemium();
  const loadSkill = useSkillStore((state) => state.loadSkill);

  useEffect(() => {
    if (skillId && user) {
      loadSkill(skillId as string, user.id, shouldSyncToCloud);
    }
  }, [skillId, user, shouldSyncToCloud, loadSkill]);

  return <SkillPlayer />;
}
