# JSON → Interactive UI Pipeline

This document describes the **first-pass implementation** that converts a structured *Skill JSON* file into an interactive lesson experience inside the Expo / React-Native app.

> Goal: prove that *one JSON object in* ➜ *one working screen out* is feasible, laying the groundwork for richer UI and backend connectivity.

---

## 1. Summary of what was added

| File / Folder | Purpose |
|---------------|---------|
| `src/types/skill.ts` | TypeScript source of truth for the Skill / Level / Lesson / Exercise schema. |
| `src/fixtures/dummySkill.json` | Comprehensive dummy data covering every exercise type + edge-cases. |
| `src/features/skillPlayer/` | New feature module responsible for rendering skills. Contains registry, store, renderer, and stub components. |
| `src/app/learn/[skillId].tsx` | Dynamic route that loads a skill (dev: from fixture) and shows the player. |
| `docs/json_to_ui_pipeline.md` | (This file) design-time documentation. |

---

## 2. <PERSON><PERSON><PERSON> Contract

See `src/types/skill.ts`.  Highlights:

```ts
export type Exercise =
  | { type: "text-info";  payload: { html: string } }
  | { type: "single-choice"; payload: { prompt: string; choices: string[]; answerIndex: number } }
  | { type: "multi-choice";  payload: { prompt: string; choices: string[]; answerIndices: number[] } }
  | { type: "fill-blank";    payload: { promptWithBlank: string; answer: string; choices?: string[] } }
  | { type: "drag-order";    payload: { items: string[]; correctOrderIndices: number[] } };
```

Why:

* **Explicit union** prevents ambiguous structures.
* Keeps renderer extensible—add new types via union + registry update.

---

## 3. Dummy Skill Fixture

`src/fixtures/dummySkill.json` contains:

* Two levels (Recognition & Application) ➜ Two lessons each ➜ Six exercises each.
* Every exercise type represented, plus edge-cases (empty strings, duplicate items, etc.).
* Enables offline dev & rapid UI iteration with `router.push("/learn/skill-dummy-fractions-001")`.

---

## 4. The `skillPlayer` Feature Module

### 4.1 Registry (`registry.ts`)

Maps `exercise.type` strings to their React component implementations.

### 4.2 State Store (`store.ts`)

* **zustand** chosen for minimal boilerplate.
* Holds current skill, indexes, answers, lives.
* Development mode loads fixture; later swaps to Convex query.

### 4.3 Renderer (`ExerciseRenderer.tsx`)

Tiny wrapper that selects the correct component from the registry and passes props.

### 4.4 `SkillPlayer.tsx`

Orchestrator:

1. Picks current lesson/exercise.
2. Shows a simple lesson title.
3. Renders the exercise.
4. Provides a `Next` button (MVP navigation).

### 4.5 Stub Components

Each exercise type has a minimalist component in `components/`:

* `TextInfo` – strips HTML & shows plain text.
* `SingleChoice` – radio-style selection.
* `MultiChoice` – checkbox-style.
* `FillBlank` – text input plus optional suggestion buttons.
* `DragOrder` – tap-to-sequence (will upgrade to real drag later).

All components call `onAnswer()` so the store can record user input.

---

## 5. Routing

`src/app/learn/[skillId].tsx` is a dynamic Expo-Router screen.

* Reads `skillId` from URL params.
* Calls `useSkillStore.getState().loadSkill()`.  (Fixture for now.)
* Renders `<SkillPlayer/>`.

This plugs into your custom tab bar via the `learn` route already listed.

---

## 6. How to test right now

1. `pnpm expo start` (or yarn / npm).
2. In the app, from anywhere run:

```ts
import { router } from "expo-router";
router.push("/learn/skill-dummy-fractions-001");
```

—or add a temporary button somewhere that navigates to that path.
3. You should cycle through all stub exercises using the **Next** button.

---

## 7. Design Decisions & Rationale

| Decision | Rationale |
|----------|-----------|
| **Types first** | Prevents schema drift between prompt, backend, and front-end. |
| **Registry Pattern** | Isolates each exercise UI; adding a new type touches only registry + new component. |
| **zustand** store | Lightweight, react-friendly, good for optimistic updates. |
| **Fixture JSON** | Allows UI development before backend query/mutation are ready. |
| **Dynamic route** | Deep-linkable lessons; mirrors Duolingo URL pattern. |
| **Stub UI** | Ship fast; replace visuals incrementally without breaking logic. |

---

## 8. Next Steps (Roadmap excerpt)

1. Replace fixture loader with Convex `getSkill` query & `submitAnswer` mutation.
2. Add progress HUD (XP, hearts) driven by the same store.
3. Upgrade `DragOrder` to use `react-native-reanimated` + `gesture-handler`.
4. Validate user answers and show correct/incorrect states.
5. Offline queue for answer submissions using MMKV.
6. Polished UI: animations, theming, haptics.

---

Happy hacking! 🎉
