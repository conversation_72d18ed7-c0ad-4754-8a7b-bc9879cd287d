import React from "react";
import {
  View,
  TextInput,
  ScrollView,
  TouchableOpacity,
  Image,
  Text,
  StyleSheet,
  Animated,
} from "react-native";
import {
  Fading<PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  LargeHeader,
  ScalingView,
  ScrollHeaderProps,
  ScrollViewWithHeaders,
  SurfaceComponentProps,
} from "@codeherence/react-native-header";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { ChivoText, HeeboText } from "@/components/ui/CustomText";
import { BlurView } from "expo-blur";
import { router } from "expo-router";
import { SKILLS } from "@/fixtures/index";
import {
  useFreemium,
  useUpgradePrompt,
} from "@/features/freemium/FreemiumProvider";
import Reanimated, {
  useSharedValue,
  useAnimatedStyle,
  withSpring,
  withTiming,
  interpolate,
} from "react-native-reanimated";
import Fuse from "fuse.js";
import { useTheme, useThemedStyles } from "~/lib/theme";
import { getThemedShadow, colorUtils } from "~/lib/themeUtils";

// Constants
const ILLUSTRATIONS = [
  require("../../../../assets/images/giraffe.png"),
  require("../../../../assets/images/fish.png"),
  require("../../../../assets/images/insect.png"),
  require("../../../../assets/images/giraffe.png"),
  require("../../../../assets/images/fish.png"),
  require("../../../../assets/images/insect.png"),
  require("../../../../assets/images/giraffe.png"),
];

const CATEGORIES = [
  { icon: "🌈", label: "Colors" },
  { icon: "✋", label: "Body" },
  { icon: "🍎", label: "Food" },
  { icon: "chevron-down", label: "" },
];

// Theme-aware course colors function
const getCourseColors = (colors: any) => [
  [colors.warning[400], colors.warning[600]], // Orange gradient
  [colors.primary[400], colors.primary[600]], // Green gradient
  [colors.secondary[400], colors.secondary[600]], // Brown gradient
];

// Transform SKILLS into LAST_LESSONS format - now theme-aware
const getLastLessons = (colors: any) => {
  console.log("Getting last lessons with colors:", colors);
  return SKILLS.map((skill, index) => ({
    id: skill.id,
    name: skill.name,
    levels: skill.levels,
    backgroundColor: colors.primary[50], // Light theme-aware background
    width: "40%",
    illustration: ILLUSTRATIONS[skill.levels.length % ILLUSTRATIONS.length],
  }));
}

// Header Components
const HeaderSurface = ({ showNavBar }: SurfaceComponentProps) => {
  const { theme, colors, isDarkMode } = useTheme();
  console.log("HeaderSurface rendering with theme:", theme);

  const animatedStyle = useAnimatedStyle(() => {
    "worklet";
    // Helper function to add alpha to hex color (worklet-compatible)
    const addAlpha = (color: string, alpha: number): string => {
      "worklet";
      const hex = color.replace("#", "");
      const alphaHex = Math.round(alpha * 255)
        .toString(16)
        .padStart(2, "0");
      return `#${hex}${alphaHex}`;
    };

    return {
      backgroundColor: addAlpha(theme.card, 0.7),
      borderBottomWidth: interpolate(showNavBar.value, [0, 1], [0, 1]),
      borderBottomColor: addAlpha(theme.border, 0.8),
      shadowColor: theme.shadow,
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: interpolate(showNavBar.value, [0, 1], [0, 0.08]),
      shadowRadius: 2,
    };
  }, [theme.card, theme.border, theme.shadow]);

  return (
    <FadingView opacity={showNavBar} style={StyleSheet.absoluteFill}>
      <BlurView
        intensity={90}
        tint={isDarkMode ? "dark" : "light"}
        style={StyleSheet.absoluteFill}
      >
        <Reanimated.View className="h-full w-full" style={animatedStyle} />
      </BlurView>
    </FadingView>
  );
};

const HeaderComponent = ({ showNavBar }: ScrollHeaderProps) => {
  const insets = useSafeAreaInsets();
  const { theme } = useTheme();
  console.log("HeaderComponent rendering with insets:", insets);

  const styles = useThemedStyles((theme, colors, isDark) =>
    StyleSheet.create({
      headerTitle: {
        fontSize: 16,
        fontWeight: "600",
        color: theme.text,
      },
    })
  );

  return (
    <Header
      showNavBar={showNavBar}
      noBottomBorder
      headerStyle={{ height: 44 + insets.top }}
      headerCenter={<Text style={styles.headerTitle}>Home</Text>}
      SurfaceComponent={HeaderSurface}
    />
  );
};

const LargeHeaderComponent = ({ scrollY }: { scrollY: any }) => {
  const { theme } = useTheme();
  console.log("LargeHeaderComponent rendering with scrollY:", scrollY);

  return (
    <LargeHeader>
      <ScalingView scrollY={scrollY}>
        <Text className="text-4xl font-bold" style={{ color: theme.text }}>
          Choose what
        </Text>
        <Text className="text-3xl" style={{ color: theme.text }}>
          to learn today?
        </Text>
      </ScalingView>
    </LargeHeader>
  );
};

// Reusable Components
const SearchBar = ({
  value,
  onChangeText,
  onCancel,
}: {
  value: string;
  onChangeText: (text: string) => void;
  onCancel: () => void;
}) => {
  const { theme, colors } = useTheme();
  console.log("SearchBar rendering with value:", value);

  return (
    <View className="px-7">
      <View
        className="flex-row items-center rounded-3xl px-4 py-5 mb-6"
        style={{ backgroundColor: theme.muted }}
      >
        <Ionicons name="search" size={22} color={theme.secondaryText} />
        <TextInput
          placeholder="Search..."
          placeholderTextColor={theme.secondaryText}
          style={{
            fontSize: 16,
            fontWeight: "bold",
            color: theme.text,
          }}
          className="flex-1 ml-3 text-base font-bold"
          value={value}
          onChangeText={onChangeText}
        />
        {!!value && (
          <TouchableOpacity onPress={onCancel} className="p-2 -mr-2">
            <Ionicons
              name="close-circle"
              size={22}
              color={theme.secondaryText}
            />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const CategoryItem = ({
  icon,
  label,
  onPress,
  isActive = false,
}: {
  icon: string;
  label: string;
  onPress?: () => void;
  isActive?: boolean;
}) => {
  const { theme, colors } = useTheme();
  const scale = useSharedValue(1);
  console.log("CategoryItem rendering:", { icon, label, isActive });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
    };
  });

  const handlePressIn = () => {
    scale.value = withSpring(0.92, { damping: 15, stiffness: 150 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 150 });
  };

  return (
    <TouchableOpacity
      className="items-center"
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={0.7}
      disabled={!onPress}
    >
      <Reanimated.View
        className="w-16 h-16 rounded-full items-center justify-center mb-2"
        style={[
          {
            backgroundColor: isActive ? colors.primary[100] : theme.muted,
            elevation: 1,
            shadowColor: theme.shadow,
            shadowOpacity: 0.1,
            shadowRadius: 4,
            shadowOffset: { width: 0, height: 2 },
          },
          animatedStyle,
        ]}
      >
        {icon === "chevron-down" ? (
          <Ionicons
            name="chevron-down"
            size={24}
            color={isActive ? colors.primary[600] : theme.secondaryText}
          />
        ) : icon.startsWith("ion-") ? (
          <Ionicons
            name={icon.replace("ion-", "") as any}
            size={24}
            color={isActive ? colors.primary[600] : theme.secondaryText}
          />
        ) : (
          <ChivoText
            className="text-3xl"
            style={{ color: isActive ? colors.primary[500] : theme.text }}
          >
            {icon}
          </ChivoText>
        )}
      </Reanimated.View>
      {label ? (
        <ChivoText
          className="text-sm font-bold mt-1"
          style={{
            color: isActive ? colors.primary[600] : theme.secondaryText,
          }}
        >
          {label}
        </ChivoText>
      ) : null}
    </TouchableOpacity>
  );
};

const PremiumBadge = () => {
  const { colors } = useTheme();
  console.log("PremiumBadge rendering");
  return (
    <View
      className="absolute top-2 right-2 rounded-full px-2 py-1 flex-row items-center"
      style={{ backgroundColor: colors.warning[500] }}
    >
      <Ionicons name="diamond" size={12} color={colors.white} />
      <Text className="text-xs font-bold ml-1" style={{ color: colors.white }}>
        PRO
      </Text>
    </View>
  );
};

const FreeBadge = () => {
  const { colors } = useTheme();
  console.log("FreeBadge rendering");
  return (
    <View
      className="absolute top-2 right-2 rounded-full px-2 py-1 flex-row items-center"
      style={{ backgroundColor: colors.success[500] }}
    >
      <Ionicons name="download" size={12} color={colors.white} />
      <Text className="text-xs font-bold ml-1" style={{ color: colors.white }}>
        FREE
      </Text>
    </View>
  );
};

const CourseCard = ({ course, index, isPremium, isFreeTier, onPress }: any) => {
  const { colors, theme } = useTheme();
  const scale = useSharedValue(1);
  const elevation = useSharedValue(1);
  const courseColors = getCourseColors(colors);
  console.log("CourseCard rendering:", { course: course.name, index, isPremium });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }],
      shadowOpacity: interpolate(elevation.value, [1, 1.5], [0.1, 0.25]),
      shadowRadius: interpolate(elevation.value, [1, 1.5], [4, 8]),
      elevation: interpolate(elevation.value, [1, 1.5], [2, 6]),
    };
  });

  const handlePressIn = () => {
    scale.value = withSpring(0.97, { damping: 15, stiffness: 150 });
    elevation.value = withTiming(1.5, { duration: 150 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 150 });
    elevation.value = withTiming(1, { duration: 150 });
  };

  return (
    <TouchableOpacity
      key={course.id || index}
      onPress={onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={0.9}
    >
      <Reanimated.View
        className="rounded-2xl p-4 mb-4 flex-row items-center"
        style={[
          {
            backgroundColor: courseColors[index % courseColors.length][0],
            shadowColor: theme.shadow,
            shadowOffset: { width: 0, height: 2 },
          },
          animatedStyle,
        ]}
      >
        <View className="flex-1">
          <Text
            className="text-xl font-bold mb-1"
            style={{ color: colors.white }}
          >
            {course.name}
          </Text>
          <Text
            className="text-sm mb-2"
            style={{ color: `${colors.white}CC` }} // 0.8 alpha = CC in hex
          >
            {course.description}
          </Text>
          <Text
            className="text-xs"
            style={{ color: `${colors.white}B3` }} // 0.7 alpha = B3 in hex
          >
            {course.levels?.[0]?.lessons?.length || 0} lessons
            {isPremium && isFreeTier && " • Premium"}
          </Text>
        </View>

        <View
          className="w-16 h-16 rounded-full items-center justify-center ml-4"
          style={{ backgroundColor: `${colors.white}33` }} // 0.2 alpha = 33 in hex
        >
          <Text className="text-2xl">
            {index === 0 ? "📚" : index === 1 ? "📐" : "⚛️"}
          </Text>
        </View>

        {isPremium && <PremiumBadge />}
        {!isPremium && isFreeTier && <FreeBadge />}
      </Reanimated.View>
    </TouchableOpacity>
  );
};

const LessonCard = ({
  lesson,
  isPremium,
  isFreeTier,
  onPress,
  height,
}: any) => {
  const { theme, colors } = useTheme();
  const scale = useSharedValue(1);
  const elevation = useSharedValue(1);
  const rotate = useSharedValue("0deg");
  console.log("LessonCard rendering:", { lesson: lesson.name, isPremium, height });

  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }, { rotate: rotate.value }],
      shadowOpacity: interpolate(elevation.value, [1, 1.5], [0.1, 0.22]),
      shadowRadius: interpolate(elevation.value, [1, 1.5], [4, 8]),
      elevation: interpolate(elevation.value, [1, 1.5], [2, 5]),
    };
  });

  const handlePressIn = () => {
    scale.value = withSpring(0.95, { damping: 15, stiffness: 150 });
    elevation.value = withTiming(1.5, { duration: 150 });
    rotate.value = withSpring("1deg", { damping: 15, stiffness: 150 });
  };

  const handlePressOut = () => {
    scale.value = withSpring(1, { damping: 15, stiffness: 150 });
    elevation.value = withTiming(1, { duration: 150 });
    rotate.value = withSpring("0deg", { damping: 15, stiffness: 150 });
  };

  return (
    <TouchableOpacity
      onPress={onPress}
      key={lesson.name}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      activeOpacity={0.9}
    >
      <Reanimated.View
        className="rounded-3xl p-4 mb-4 border-2"
        style={[
          {
            backgroundColor: theme.card,
            borderColor: theme.border,
            height,
            shadowColor: theme.shadow,
            shadowOffset: { width: 0, height: 4 },
          },
          animatedStyle,
        ]}
      >
        <View className="flex-1">
          <Image
            source={lesson.illustration}
            className="w-full items-center justify-center h-3/5 mt-2"
            resizeMode="contain"
          />
          {isPremium && <PremiumBadge />}
          {!isPremium && isFreeTier && <FreeBadge />}
        </View>
        <View>
          <ChivoText
            fontWeight="BOLD"
            className="text-xl mb-1"
            style={{ color: theme.text }}
          >
            {lesson.name}
          </ChivoText>
          <ChivoText className="text-sm" style={{ color: theme.secondaryText }}>
            {lesson.levels.length} lessons
            {isPremium && isFreeTier && " • Premium"}
          </ChivoText>
        </View>
      </Reanimated.View>
    </TouchableOpacity>
  );
};

export default function LearnScreen() {
  const { bottom } = useSafeAreaInsets();
  const { isFreeTier } = useFreemium();
  const { promptForFeature } = useUpgradePrompt();
  const { colors, theme } = useTheme();
  const [searchQuery, setSearchQuery] = React.useState("");
  console.log("LearnScreen rendering with theme:", theme.name || "unknown");

  // Get theme-aware data - memoize to prevent infinite re-renders
  const LAST_LESSONS = React.useMemo(() => getLastLessons(colors), [colors]);
  const [filteredLessons, setFilteredLessons] = React.useState(LAST_LESSONS);

  const fuse = React.useMemo(() => {
    const options = {
      keys: ["name"],
      includeScore: true,
      threshold: 0.4,
    };
    console.log("Creating Fuse instance with lessons:", LAST_LESSONS.length);
    return new Fuse(LAST_LESSONS, options);
  }, [LAST_LESSONS]);

  // Update filtered lessons when LAST_LESSONS changes (theme change)
  React.useEffect(() => {
    console.log("LAST_LESSONS changed, updating filtered lessons");
    setFilteredLessons(LAST_LESSONS);
  }, [LAST_LESSONS]);

  React.useEffect(() => {
    console.log("Search query changed:", searchQuery);
    if (searchQuery === "") {
      setFilteredLessons(LAST_LESSONS);
    } else {
      const results = fuse.search(searchQuery);
      console.log("Search results:", results.length);
      const filtered = results.map((result) => result.item);
      setFilteredLessons(filtered);
    }
  }, [searchQuery, fuse, LAST_LESSONS]);

  const handleCoursePress = (courseId: string, isPremium = false) => {
    console.log("Course pressed:", { courseId, isPremium });
    if (isPremium && isFreeTier) {
      promptForFeature("Premium Course Access");
      return;
    }
    router.push(`/(app)/(authenticated)/(tabs)/learn/${courseId}`);
  };

  return (
    <View className="w-full h-full rounded-xl">
      <ScrollViewWithHeaders
        absoluteHeader
        HeaderComponent={HeaderComponent}
        LargeHeaderComponent={LargeHeaderComponent}
        style={styles.container}
        contentContainerStyle={{ paddingBottom: bottom + 12 }}
      >
        <ScrollView showsVerticalScrollIndicator={false} className="pt-4">
          <SearchBar
            value={searchQuery}
            onChangeText={setSearchQuery}
            onCancel={() => setSearchQuery("")}
          />

          {/* Categories */}
          <View className="flex-row justify-around mb-8 px-2">
            {CATEGORIES.map((cat, idx) => (
              <CategoryItem
                key={idx}
                icon={cat.icon}
                label={cat.label}
                onPress={
                  cat.icon === "chevron-down"
                    ? () => router.push("/(app)/(authenticated)/categories")
                    : undefined
                }
              />
            ))}
          </View>

          {/* Available Courses */}

          {/* Free Courses */}
          <View className="px-5 pt-12">
            <HeeboText
              fontWeight="BOLD"
              className="text-4xl mb-4"
              style={{ color: theme.text }}
            >
              Free Courses
            </HeeboText>
          </View>
          <View className="px-5 mb-16">
            {filteredLessons.length === 0 ? (
              <View className="items-center justify-center h-40">
                <HeeboText
                  fontWeight="BOLD"
                  className="text-xl"
                  style={{ color: theme.secondaryText }}
                >
                  No courses found.
                </HeeboText>
                <HeeboText
                  className="mt-2"
                  style={{ color: theme.secondaryText }}
                >
                  Try a different search term.
                </HeeboText>
              </View>
            ) : (
              <View className="flex-row">
                {/* Left Column */}
                <View className="flex-1 mr-2">
                  {filteredLessons
                    .filter((_, i) => i % 2 === 0)
                    .map((lesson, index) => {
                      const isPremium = index > 2;
                      const height = lesson.width === "55%" ? 320 : 260;
                      return (
                        <LessonCard
                          key={lesson.name}
                          lesson={lesson}
                          isPremium={isPremium}
                          isFreeTier={isFreeTier}
                          height={height}
                          onPress={() =>
                            handleCoursePress(lesson.id || "1", isPremium)
                          }
                        />
                      );
                    })}
                </View>

                {/* Right Column */}
                <View className="flex-1 ml-2">
                  {filteredLessons
                    .filter((_, i) => i % 2 === 1)
                    .map((lesson) => {
                      const isPremium = false;
                      const height = lesson.width === "55%" ? 260 : 320;
                      return (
                        <LessonCard
                          key={lesson.name}
                          lesson={lesson}
                          isPremium={isPremium}
                          isFreeTier={isFreeTier}
                          height={height}
                          onPress={() =>
                            handleCoursePress(lesson.id || "1", isPremium)
                          }
                        />
                      );
                    })}
                </View>
              </View>
            )}
          </View>
        </ScrollView>
      </ScrollViewWithHeaders>
    </View>
  );
}

// Main component styles are now created using useThemedStyles within components
const styles = StyleSheet.create({
  container: {
    flex: 1,
    zIndex: -100,
  },
  contentContainer: {
    paddingTop: 44,
  },
  boxes: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    gap: 12,
    flexWrap: "wrap",
  },
  title: { fontSize: 32, fontWeight: "bold" },
  leftHeader: { gap: 2 },
});
