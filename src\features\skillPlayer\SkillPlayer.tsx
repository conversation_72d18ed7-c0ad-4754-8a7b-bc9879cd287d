import React, { useEffect, useState } from "react";
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { router } from "expo-router";
import { Ionicons } from "@expo/vector-icons";
import { useUser } from "@clerk/clerk-expo";
import { useSkillStore } from "./store";
import ExerciseRenderer from "./ExerciseRenderer";
import ProgressBar from "./components/ProgressBar";
import QuestionCounter from "./components/QuestionCounter";
import AnswerFeedback from "./components/AnswerFeedback";
import LessonCompletionScreen from "./components/LessonCompletionScreen";
import CheckContinueButton from "./ui/CheckContinueButton";
import StreakCounter from "./components/StreakCounter";
import { useNavigation } from "expo-router";
import { audioService } from "./services/AudioService";
import { hapticService } from "./services/HapticService";
import { useFreemium, useUpgradePrompt } from "@/features/freemium/FreemiumProvider";

export default function SkillPlayer() {
  const { user } = useUser();
  const { isFreeTier, shouldSyncToCloud, canAccessFeature } = useFreemium();
  const { promptForFeature } = useUpgradePrompt();

  const {
    skill,
    lessonIndex,
    exerciseIndex,
    next,
    answer,
    answers,
    answerFeedback,
    buttonStates,
    showCompletion,
    currentLessonStats,
    validateAnswer,
    clearFeedback,
    startNextLesson,
    reviewCurrentLesson,
    goToHome,
    saveProgressLocally,
    syncToCloudIfNeeded
  } = useSkillStore();
  const navigation = useNavigation();

  // Engagement state
  const [currentStreak, setCurrentStreak] = useState(0);
  const [showParticles, setShowParticles] = useState(false);
  const [particleType, setParticleType] = useState<'correct' | 'celebration' | 'milestone'>('correct');

  // Initialize audio service
  useEffect(() => {
    audioService.initialize();
    return () => {
      audioService.cleanup();
    };
  }, []);

  console.log(skill);

  if (!skill) {
    return (
      <SafeAreaView style={styles.center}><Text>Loading skill…</Text></SafeAreaView>
    );
  }

  // Show completion screen if lesson/level/skill is completed
  if (showCompletion) {
    const currentLevel = skill.levels[0];
    const currentLesson = currentLevel.lessons[lessonIndex];
    const canStartNext = lessonIndex < currentLevel.lessons.length - 1;

    return (
      <LessonCompletionScreen
        completionType={showCompletion}
        lessonName={currentLesson.name}
        lessonStats={currentLessonStats}
        onStartNextLesson={startNextLesson}
        onReviewLesson={reviewCurrentLesson}
        onGoHome={goToHome}
        canStartNextLesson={canStartNext}
      />
    );
  }

  const lesson = skill.levels[0].lessons[lessonIndex];
  const exercise = lesson.exercises[exerciseIndex];
  const progress = (exerciseIndex + 1) / lesson.exercises.length;
  const isAnswered = answers[exercise.id] !== undefined;
  const currentQuestionNumber = exerciseIndex + 1;
  const totalQuestions = lesson.exercises.length;
  const currentFeedback = answerFeedback[exercise.id];
  const currentButtonState = buttonStates[exercise.id] || (exercise.type === 'text-info' ? 'continue' : 'check');

  const handleAnswer = (value: unknown) => {
    answer(exercise.id, value);
    // Haptic feedback for answer selection
    hapticService.onAnswerSelect();
    // Don't validate automatically anymore - wait for CHECK button
  };

  const handleCheck = () => {
    validateAnswer(exercise.id);

    // Check if answer was correct and update streak
    const currentFeedback = answerFeedback[exercise.id];
    if (currentFeedback?.isCorrect) {
      const newStreak = currentStreak + 1;
      setCurrentStreak(newStreak);

      // Trigger particle effects
      setParticleType('correct');
      setShowParticles(true);

      // Check for streak milestones
      if (newStreak % 5 === 0) {
        hapticService.onStreakAchievement();
        audioService.playStreak();
      }
    } else {
      // Reset streak on incorrect answer
      setCurrentStreak(0);
    }
  };

  const handleContinue = async () => {
    next();

    // Exercise completion feedback
    hapticService.onExerciseComplete();
    audioService.playExerciseComplete();

    // Save progress locally after each exercise
    try {
      await saveProgressLocally();

      // Sync to cloud if user is premium
      if (shouldSyncToCloud) {
        await syncToCloudIfNeeded();
      }
    } catch (error) {
      console.error('Failed to save progress:', error);
    }
  };

  const handleFeedbackComplete = () => {
    clearFeedback(exercise.id);
  };

  const handleParticleComplete = () => {
    setShowParticles(false);
  };

  const handleStreakMilestone = (streak: number) => {
    // Trigger milestone celebration
    setParticleType('milestone');
    setShowParticles(true);
    hapticService.onStreakAchievement();
    audioService.playMilestone();
  };

  // For CHECK button: enabled when user has answered (except text-info which goes straight to continue)
  // For CONTINUE button: always enabled after validation
  const isButtonDisabled = () => {
    if (exercise.type === 'text-info') {
      return false; // Text info can always continue
    }

    if (currentButtonState === 'check') {
      return !isAnswered; // CHECK button disabled until user answers
    }

    return false; // CONTINUE button is always enabled after validation
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* Particle Effects Overlay */}
     

      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()} hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}>
          <Ionicons name="arrow-back" size={24} color="#a1a1aa" />
        </TouchableOpacity>
        <ProgressBar progress={progress} animated={true} />
        <QuestionCounter current={currentQuestionNumber} total={totalQuestions} />
      </View>

      <View style={styles.contentWrapper}>
        <AnswerFeedback
          feedback={currentFeedback}
          onAnimationComplete={handleFeedbackComplete}
        />

        <ScrollView style={styles.contentContainer} contentContainerStyle={{ paddingBottom: 24 }}>
          <View style={styles.lessonHeader}>
            <Text style={styles.lessonTitle}>{lesson.name}</Text>
           
          </View>

          <View style={{
            height: 3,
            backgroundColor: '#000000',
            marginBottom: 44,
            marginTop: 16,
            width: '100%'
          }} />

          <ExerciseRenderer
            exercise={exercise}
            currentAnswer={answers[exercise.id]}
            onAnswer={handleAnswer}
            feedback={currentFeedback}
          />
        </ScrollView>
      </View>

      <CheckContinueButton
        buttonState={currentButtonState}
        onCheck={handleCheck}
        onContinue={handleContinue}
        disabled={isButtonDisabled()}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#FAF9F7", paddingTop: 8 },
  center: { flex: 1, justifyContent: "center", alignItems: "center" },
  header: {
    flexDirection: "row",
    alignItems: "center",
    gap: 20,
    paddingBottom: 8,
    paddingHorizontal: 24,
  },
  contentWrapper: {
    flex: 1,
    position: "relative",
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  lessonTitle: { fontSize: 28, fontWeight: "bold", marginTop: 16,  },
  lessonHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginVertical: 0,
  },
  offlineBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#ECFDF5',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  offlineText: {
    marginLeft: 4,
    fontSize: 12,
    fontWeight: '600',
    color: '#10B981',
  },
  cloudBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  cloudText: {
    marginLeft: 4,
    fontSize: 12,
    fontWeight: '600',
    color: '#6366F1',
  },
});