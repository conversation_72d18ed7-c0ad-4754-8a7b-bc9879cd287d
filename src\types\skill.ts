export type UUID = string;

// -------------------- Exercise Types --------------------
export type TextInfoExercise = {
  id: UUID;
  type: "text-info";
  payload: {
    markdown: string;
  };
};

export type SingleChoiceExercise = {
  id: UUID;
  type: "single-choice";
  payload: {
    prompt: string;
    choices: string[];
    answerIndex: number;
  };
};

export type MultiChoiceExercise = {
  id: UUID;
  type: "multi-choice";
  payload: {
    prompt: string;
    choices: string[];
    answerIndices: number[];
  };
};

export type FillBlankExercise = {
  id: UUID;
  type: "fill-blank";
  payload: {
    promptWithBlank: string;
    answer: string;
    choices?: string[]; // optional – UI can render as suggestions
  };
};

export type DragOrderExercise = {
  id: UUID;
  type: "drag-order";
  payload: {
    items: string[];
    correctOrderIndices: number[];
  };
};

export type TrueFalseExercise = {
  id: UUID;
  type: "true-false";
  payload: {
    prompt: string;
    answer: boolean;
  };
};

export type MatchingPairsExercise = {
  id: UUID;
  type: "matching-pairs";
  payload: {
    prompt: string;
    left: string[];
    right: string[];
    pairs: [number, number][]; // [leftIndex, rightIndex]
  };
};

// Union of all
export type Exercise =
  | TextInfoExercise
  | SingleChoiceExercise
  | MultiChoiceExercise
  | FillBlankExercise
  | DragOrderExercise
  | TrueFalseExercise
  | MatchingPairsExercise;

// -------------------- Hierarchy --------------------
export interface Lesson {
  name: string;
  exercises: Exercise[];
}

export interface Level {
  name: string;
  lessons: Lesson[];
}

export interface Skill {
  id: UUID;
  name: string;
  description: string;
  version: number;
  levels: Level[];
}