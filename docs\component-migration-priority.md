# Component Migration Priority List

## 🎯 Migration Strategy

Components are prioritized based on:
- **User Visibility**: How often users see the component
- **Visual Impact**: How much the component affects overall app appearance
- **Theme Dependency**: How many hardcoded colors need migration
- **Complexity**: Implementation difficulty and testing requirements

## 🔥 HIGH PRIORITY (Week 1)

### 1. CheckContinueButton ⭐⭐⭐⭐⭐
**Path**: `src/features/skillPlayer/ui/CheckContinueButton.tsx`
**Impact**: CRITICAL - Most visible interactive element in learning flow
**Hardcoded Colors**: 6+ colors (purple button, shadows, disabled states)
**Complexity**: Medium (animations, states)
**Estimated Time**: 2-3 hours

### 2. AnswerFeedback ⭐⭐⭐⭐⭐
**Path**: `src/features/skillPlayer/components/AnswerFeedback.tsx`
**Impact**: CRITICAL - Core user feedback mechanism
**Hardcoded Colors**: 8+ colors (success/error states, backgrounds, borders)
**Complexity**: Medium (animations, conditional styling)
**Estimated Time**: 2-3 hours

### 3. MultiChoice ⭐⭐⭐⭐
**Path**: `src/features/skillPlayer/components/MultiChoice.tsx`
**Impact**: HIGH - Primary exercise type
**Hardcoded Colors**: 5+ colors (options, selections, feedback states)
**Complexity**: Medium (interactive states)
**Estimated Time**: 2 hours

### 4. Tab Navigation ⭐⭐⭐⭐
**Path**: `src/app/(app)/(authenticated)/(tabs)/_layout.tsx`
**Impact**: HIGH - Always visible navigation
**Hardcoded Colors**: 4+ colors (backgrounds, active states, borders)
**Complexity**: Low-Medium
**Estimated Time**: 1-2 hours

## 🟡 MEDIUM PRIORITY (Week 2)

### 5. SingleChoice ⭐⭐⭐
**Path**: `src/features/skillPlayer/components/SingleChoice.tsx`
**Impact**: MEDIUM - Common exercise type
**Hardcoded Colors**: 4+ colors
**Complexity**: Low-Medium
**Estimated Time**: 1-2 hours

### 6. Profile Screen ⭐⭐⭐
**Path**: `src/app/(app)/(authenticated)/(tabs)/profile.tsx`
**Impact**: MEDIUM - User engagement screen
**Hardcoded Colors**: 10+ colors (gradients, stats, achievements)
**Complexity**: High (gradients, complex layouts)
**Estimated Time**: 3-4 hours

### 7. Home/Index Screen ⭐⭐⭐
**Path**: `src/app/(app)/(authenticated)/(tabs)/index.tsx`
**Impact**: MEDIUM - Entry point
**Hardcoded Colors**: 6+ colors (search, categories, cards)
**Complexity**: Medium
**Estimated Time**: 2-3 hours

### 8. TrueFalse Component ⭐⭐
**Path**: `src/features/skillPlayer/components/TrueFalse.tsx`
**Impact**: MEDIUM - Exercise type
**Hardcoded Colors**: 3+ colors
**Complexity**: Low
**Estimated Time**: 1 hour

## 🟢 LOW PRIORITY (Week 3)

### 9. LessonCompletionScreen ⭐⭐
**Path**: `src/features/skillPlayer/components/LessonCompletionScreen.tsx`
**Impact**: LOW - Occasional use
**Hardcoded Colors**: 5+ colors
**Complexity**: Medium (celebrations, animations)
**Estimated Time**: 2 hours

### 10. ProgressBar ⭐⭐
**Path**: `src/features/skillPlayer/components/ProgressBar.tsx`
**Impact**: LOW - Already theme-aware in onboarding
**Hardcoded Colors**: 2+ colors
**Complexity**: Low
**Estimated Time**: 30 minutes

### 11. Onboarding Components ⭐
**Path**: `components/onboarding/*`
**Impact**: LOW - Already mostly theme-aware
**Hardcoded Colors**: Few remaining
**Complexity**: Low
**Estimated Time**: 1-2 hours total

### 12. Settings/Premium Screens ⭐
**Path**: Various settings screens
**Impact**: LOW - Infrequent use
**Hardcoded Colors**: Variable
**Complexity**: Low-Medium
**Estimated Time**: 1-2 hours each

## 📋 Migration Checklist Template

```markdown
### Component: [ComponentName]

**Pre-Migration:**
- [ ] Take screenshots (light mode)
- [ ] Document current color usage
- [ ] Identify all hardcoded colors
- [ ] Note any animations/transitions

**Migration:**
- [ ] Import theme utilities
- [ ] Replace hardcoded colors with theme tokens
- [ ] Update StyleSheet to use useThemedStyles
- [ ] Test component in isolation

**Testing:**
- [ ] Test in light mode
- [ ] Test in dark mode
- [ ] Test all interactive states
- [ ] Test animations/transitions
- [ ] Verify accessibility contrast

**Post-Migration:**
- [ ] Take comparison screenshots
- [ ] Update component documentation
- [ ] Mark as theme-compliant
- [ ] Performance check
```

## 🎯 Success Criteria

### Per Component:
- [ ] Zero hardcoded colors remaining
- [ ] Supports both light and dark themes
- [ ] Maintains visual consistency
- [ ] No performance regressions
- [ ] Animations work smoothly

### Overall Migration:
- [ ] All high-priority components migrated
- [ ] Theme switching works seamlessly
- [ ] Visual regression tests pass
- [ ] User experience unchanged
- [ ] Code maintainability improved

## 🚀 Implementation Order

**Day 1-2**: CheckContinueButton + AnswerFeedback
**Day 3-4**: MultiChoice + Tab Navigation
**Day 5-7**: SingleChoice + Profile Screen
**Day 8-10**: Home Screen + TrueFalse
**Day 11-14**: Remaining components + testing

## 📊 Progress Tracking

| Component | Status | Colors Fixed | Testing | Complete |
|-----------|--------|--------------|---------|----------|
| CheckContinueButton | ✅ | 6/6 | ⏳ | ✅ |
| AnswerFeedback | ✅ | 8/8 | ⏳ | ✅ |
| MultiChoice | ✅ | 5/5 | ⏳ | ✅ |
| Tab Navigation | ✅ | 4/4 | ⏳ | ✅ |
| Profile Screen | ✅ | 15/15 | ⏳ | ✅ |
| Home Screen | ✅ | 8/8 | ⏳ | ✅ |
| SingleChoice | ⏳ | 0/4 | ❌ | ❌ |
| TrueFalse | ⏳ | 0/3 | ❌ | ❌ |

**Legend**: ⏳ Pending | 🔄 In Progress | ✅ Complete | ❌ Not Started

This priority list ensures we tackle the most impactful components first while building momentum for the complete migration.
