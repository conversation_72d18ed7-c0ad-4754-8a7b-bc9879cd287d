# Freemium System Migration Guide

## Overview

This guide helps you migrate your existing Duolingo-style learning app to the new offline-first freemium architecture while maintaining existing functionality and user data.

## 🚀 **Quick Start Implementation**

### **Step 1: Install Dependencies**

The freemium system uses the same dependencies as the engagement enhancements:

```bash
npm install @react-native-async-storage/async-storage @react-native-community/slider
```

### **Step 2: Wrap Your App with FreemiumManager**

```typescript
// App.tsx or your root component
import FreemiumManager from './src/features/freemium/FreemiumManager';

export default function App() {
  return (
    <FreemiumManager>
      {/* Your existing app components */}
      <YourExistingApp />
    </FreemiumManager>
  );
}
```

### **Step 3: Replace SkillPlayer with FreemiumSkillPlayer**

```typescript
// Before
import SkillPlayer from './src/features/skillPlayer/SkillPlayer';

// After
import FreemiumSkillPlayer from './src/features/freemium/components/FreemiumSkillPlayer';

// Usage
<FreemiumSkillPlayer skillId="spanish-basics-offline" />
```

### **Step 4: Add Course Selection**

```typescript
import FreemiumCourseSelection from './src/features/freemium/components/FreemiumCourseSelection';

function CourseSelectionScreen() {
  const handleCourseSelect = (courseId: string) => {
    // Navigate to FreemiumSkillPlayer with courseId
    router.push(`/skill/${courseId}`);
  };

  const handleUpgradePress = () => {
    // Show upgrade screen
    setShowUpgrade(true);
  };

  return (
    <FreemiumCourseSelection
      onCourseSelect={handleCourseSelect}
      onUpgradePress={handleUpgradePress}
    />
  );
}
```

## 🔄 **Migration Strategies**

### **Strategy 1: Gradual Migration (Recommended)**

**Phase 1: Add Freemium Infrastructure**
- Install freemium system alongside existing code
- Test with new users only
- Validate offline functionality

**Phase 2: Migrate Free Users**
- Redirect new free users to offline system
- Maintain existing users on current system
- Monitor performance and user feedback

**Phase 3: Full Migration**
- Migrate existing free users to offline system
- Implement premium upgrade flows
- Deprecate old system

**Phase 4: Optimization**
- Optimize sync performance for premium users
- Add advanced freemium features
- Scale content library

### **Strategy 2: Feature Flag Migration**

```typescript
// Use feature flags to control rollout
const useFreemiumSystem = useFeatureFlag('freemium_system_enabled');

function SkillPlayerWrapper({ skillId }: { skillId: string }) {
  if (useFreemiumSystem) {
    return <FreemiumSkillPlayer skillId={skillId} />;
  }
  
  return <OriginalSkillPlayer skillId={skillId} />;
}
```

### **Strategy 3: A/B Testing Migration**

```typescript
// Split users into cohorts for testing
const userCohort = getUserCohort(userId);

function AppWrapper() {
  if (userCohort === 'freemium_test') {
    return (
      <FreemiumManager>
        <App />
      </FreemiumManager>
    );
  }
  
  return <OriginalApp />;
}
```

## 📊 **Data Migration**

### **Existing User Data Migration**

```typescript
// Migration utility for existing users
async function migrateUserToFreemium(userId: string) {
  try {
    // 1. Export existing progress from Convex
    const existingProgress = await convex.query(api.users.getUserProgress, { userId });
    
    // 2. Convert to local storage format
    const localProgress: LocalUserProgress = {
      userId,
      skillProgress: convertSkillProgress(existingProgress.skills),
      globalStats: convertGlobalStats(existingProgress.stats),
      lastUpdated: Date.now(),
      version: '1.0.0',
    };
    
    // 3. Save to local storage
    await localStorageService.saveUserProgress(localProgress);
    
    // 4. Mark user as migrated
    await convex.mutation(api.users.markUserMigrated, { userId });
    
    console.log(`Successfully migrated user ${userId} to freemium system`);
    
  } catch (error) {
    console.error(`Failed to migrate user ${userId}:`, error);
    throw error;
  }
}
```

### **Progress Data Conversion**

```typescript
function convertSkillProgress(convexSkills: any[]): Record<string, SkillProgress> {
  const skillProgress: Record<string, SkillProgress> = {};
  
  convexSkills.forEach(skill => {
    skillProgress[skill.skillId] = {
      skillId: skill.skillId,
      currentLessonIndex: skill.currentLesson || 0,
      completedLessons: skill.completedLessons || [],
      lessonProgress: convertLessonProgress(skill.lessons),
      totalXP: skill.xp || 0,
      lastAccessed: skill.lastAccessed || Date.now(),
    };
  });
  
  return skillProgress;
}
```

## 🔧 **Integration with Existing Systems**

### **Authentication Integration**

```typescript
// Integrate with your existing auth system
import { useUser } from '@clerk/clerk-expo'; // or your auth provider

function FreemiumWrapper() {
  const { user, isLoaded } = useUser();
  
  if (!isLoaded) {
    return <LoadingScreen />;
  }
  
  return (
    <FreemiumManager>
      <App />
    </FreemiumManager>
  );
}
```

### **Payment Integration**

```typescript
// Integrate with your payment processor
import { useSubscriptionStore } from './src/features/freemium/stores/SubscriptionStore';

async function handlePremiumUpgrade(planId: string) {
  try {
    // 1. Process payment with your payment provider
    const paymentResult = await processPayment(planId);
    
    if (paymentResult.success) {
      // 2. Update subscription in freemium system
      const { upgradeToTier } = useSubscriptionStore.getState();
      await upgradeToTier(UserTier.PREMIUM);
      
      // 3. Sync with your backend
      await syncSubscriptionStatus(user.id, UserTier.PREMIUM);
      
      // 4. Initialize cloud sync
      await smartSyncService.initialize(user.id);
    }
    
  } catch (error) {
    console.error('Upgrade failed:', error);
  }
}
```

### **Analytics Integration**

```typescript
// Track freemium metrics with your analytics provider
import { analytics } from './your-analytics-service';

// Track free user engagement
function trackFreemiumEvent(event: string, properties: any) {
  const { subscription } = useSubscriptionStore.getState();
  
  analytics.track(event, {
    ...properties,
    user_tier: subscription.tier,
    is_offline_mode: subscription.tier === UserTier.FREE,
    timestamp: Date.now(),
  });
}

// Usage in components
trackFreemiumEvent('lesson_completed', {
  skill_id: skillId,
  lesson_id: lessonId,
  is_offline: true,
});
```

## 🧪 **Testing Strategy**

### **Unit Tests**

```typescript
// Test local storage functionality
describe('LocalStorageService', () => {
  it('should save and retrieve user progress', async () => {
    const progress: LocalUserProgress = createTestProgress();
    await localStorageService.saveUserProgress(progress);
    
    const retrieved = await localStorageService.getUserProgress(progress.userId);
    expect(retrieved).toEqual(progress);
  });
});

// Test subscription management
describe('SubscriptionStore', () => {
  it('should enforce daily limits for free users', () => {
    const store = useSubscriptionStore.getState();
    store.setSubscription(createFreeSubscription());
    
    // Simulate completing 10 lessons
    for (let i = 0; i < 10; i++) {
      store.incrementDailyLessons();
    }
    
    expect(store.canCompleteMoreLessons()).toBe(false);
  });
});
```

### **Integration Tests**

```typescript
// Test offline course loading
describe('FreemiumSkillPlayer', () => {
  it('should load offline courses without database calls', async () => {
    const mockDatabaseCall = jest.fn();
    
    render(<FreemiumSkillPlayer skillId="spanish-basics-offline" />);
    
    await waitFor(() => {
      expect(screen.getByText('Spanish Basics')).toBeInTheDocument();
    });
    
    expect(mockDatabaseCall).not.toHaveBeenCalled();
  });
});
```

### **E2E Tests**

```typescript
// Test complete freemium flow
describe('Freemium User Journey', () => {
  it('should complete full free user experience', async () => {
    // 1. Start as free user
    await device.launchApp();
    
    // 2. Select offline course
    await element(by.text('Spanish Basics')).tap();
    
    // 3. Complete lessons up to daily limit
    for (let i = 0; i < 10; i++) {
      await completeLesson();
    }
    
    // 4. Hit daily limit and see upgrade prompt
    await element(by.text('Start Lesson')).tap();
    await expect(element(by.text('Daily Limit Reached'))).toBeVisible();
    
    // 5. Upgrade to premium
    await element(by.text('Upgrade Now')).tap();
    await completeUpgradeFlow();
    
    // 6. Verify unlimited access
    await element(by.text('Start Lesson')).tap();
    await expect(element(by.text('Daily Limit Reached'))).not.toBeVisible();
  });
});
```

## 📈 **Performance Monitoring**

### **Key Metrics to Track**

```typescript
// Performance metrics
interface FreemiumMetrics {
  // Cost optimization
  databaseCallsPerUser: number;
  costPerActiveUser: number;
  
  // User experience
  appLaunchTime: number;
  lessonLoadTime: number;
  offlineReliability: number;
  
  // Business metrics
  freeToPremiuConversion: number;
  dailyActiveUsers: number;
  retentionRate: number;
}

// Monitoring implementation
function trackPerformanceMetrics() {
  const startTime = performance.now();
  
  // Track app launch performance
  const onAppReady = () => {
    const launchTime = performance.now() - startTime;
    analytics.track('app_launch_time', { duration: launchTime });
  };
  
  // Track database call reduction
  const originalFetch = fetch;
  let databaseCalls = 0;
  
  window.fetch = (...args) => {
    if (args[0].includes('convex')) {
      databaseCalls++;
      analytics.track('database_call', { endpoint: args[0] });
    }
    return originalFetch(...args);
  };
}
```

## 🚨 **Common Pitfalls & Solutions**

### **Pitfall 1: Data Inconsistency**

**Problem**: Local and cloud data getting out of sync

**Solution**:
```typescript
// Implement conflict resolution
async function resolveDataConflict(localData: any, cloudData: any) {
  // Use timestamp-based resolution
  if (localData.lastUpdated > cloudData.lastUpdated) {
    return localData;
  }
  
  // For additive data, merge both
  if (localData.type === 'achievements') {
    return {
      ...cloudData,
      achievements: [...new Set([...localData.achievements, ...cloudData.achievements])],
    };
  }
  
  return cloudData;
}
```

### **Pitfall 2: Storage Limits**

**Problem**: Local storage filling up on devices

**Solution**:
```typescript
// Implement storage cleanup
async function cleanupOldData() {
  const storageSize = await localStorageService.getStorageSize();
  const maxSize = 50 * 1024 * 1024; // 50MB limit
  
  if (storageSize > maxSize) {
    // Remove old lesson data beyond 90 days
    await localStorageService.cleanupOldLessonData(90);
    
    // Compress achievement data
    await localStorageService.compressAchievements();
  }
}
```

### **Pitfall 3: Upgrade Flow Interruption**

**Problem**: Users losing progress during upgrade

**Solution**:
```typescript
// Implement safe upgrade process
async function safeUpgradeProcess(userId: string) {
  try {
    // 1. Backup local data
    const backup = await localStorageService.createBackup(userId);
    
    // 2. Attempt cloud migration
    await migrateToCloud(userId);
    
    // 3. Verify migration success
    const cloudData = await verifyCloudData(userId);
    
    if (!cloudData.isValid) {
      // 4. Restore from backup if migration failed
      await localStorageService.restoreFromBackup(backup);
      throw new Error('Migration verification failed');
    }
    
    // 5. Complete upgrade
    await completeUpgrade(userId);
    
  } catch (error) {
    // Ensure user doesn't lose progress
    await handleUpgradeFailure(userId, error);
  }
}
```

## ✅ **Migration Checklist**

### **Pre-Migration**
- [ ] Install required dependencies
- [ ] Set up feature flags for gradual rollout
- [ ] Create data migration scripts
- [ ] Set up monitoring and analytics
- [ ] Test offline functionality thoroughly

### **During Migration**
- [ ] Monitor database call reduction
- [ ] Track user experience metrics
- [ ] Watch for data consistency issues
- [ ] Monitor storage usage on devices
- [ ] Track conversion rates

### **Post-Migration**
- [ ] Verify cost reduction targets met
- [ ] Confirm user experience maintained
- [ ] Optimize sync performance
- [ ] Expand offline content library
- [ ] Plan premium feature enhancements

## 🎯 **Success Criteria**

Your migration is successful when you achieve:

1. **90-95% reduction** in database calls for free users
2. **Zero degradation** in user experience metrics
3. **Maintained or improved** app performance
4. **Clear conversion funnel** from free to premium
5. **Sustainable cost structure** for free user growth

This migration guide provides a comprehensive path to implementing the offline-first freemium system while maintaining your existing user base and functionality.
