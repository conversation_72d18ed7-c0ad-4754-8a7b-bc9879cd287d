# Component Migration Example: CheckContinueButton

This document shows a complete example of migrating a component from hardcoded colors to the theme system.

## Before Migration

```typescript
// src/features/skillPlayer/ui/CheckContinueButton.tsx - BEFORE
import React, { useEffect, useState } from "react";
import { TouchableOpacity, Text, StyleSheet, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
// ... other imports

export default function CheckContinueButton({ buttonState, onCheck, onContinue, disabled }: Props) {
  const insets = useSafeAreaInsets();
  // ... component logic

  return (
    <View style={[styles.wrapper, { paddingBottom: insets.bottom || 12 }]}>
      <TouchableOpacity
        style={[styles.btn, disabled && styles.btnDisabled]}
        onPress={handlePress}
        disabled={disabled}
      >
        <Text style={styles.label}>
          {getButtonText()}
        </Text>
      </TouchableOpacity>
    </View>
  );
}

// ❌ PROBLEM: All colors are hardcoded
const styles = StyleSheet.create({
  wrapper: {
    paddingHorizontal: 24,
    paddingTop: 12,
    borderTopWidth: 1,
    borderColor: "#E5E7EB",        // ❌ Hardcoded gray
    backgroundColor: '#FFFFFF',    // ❌ Hardcoded white
  },
  btn: {
    backgroundColor: 'rgb(168, 38, 255)',  // ❌ Hardcoded purple
    shadowColor: 'rgb(140, 32, 212)',      // ❌ Hardcoded shadow
    // ... other styles
  },
  btnDisabled: {
    backgroundColor: "#E5E7EB",            // ❌ Hardcoded gray
    shadowColor: "#A1A1AA",               // ❌ Hardcoded shadow
  },
  label: {
    color: "white",                        // ❌ Hardcoded white
    // ... other styles
  },
});
```

## After Migration

```typescript
// src/features/skillPlayer/ui/CheckContinueButton.tsx - AFTER
import React, { useEffect, useState } from "react";
import { TouchableOpacity, Text, StyleSheet, View } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useTheme, useThemedStyles } from '~/lib/theme';  // ✅ Import theme
import { getThemedShadow } from '~/lib/themeUtils';       // ✅ Import utilities
// ... other imports

export default function CheckContinueButton({ buttonState, onCheck, onContinue, disabled }: Props) {
  const insets = useSafeAreaInsets();
  const { colors } = useTheme();  // ✅ Get theme context
  
  // ✅ Create theme-aware styles
  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    wrapper: {
      paddingHorizontal: 24,
      paddingTop: 12,
      borderTopWidth: 1,
      borderColor: theme.border,           // ✅ Theme color
      backgroundColor: theme.card,         // ✅ Theme color
    },
    btn: {
      position: 'relative',
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      width: '100%',
      height: 40,
      paddingHorizontal: 20,
      backgroundColor: theme.primaryButton, // ✅ Theme color
      borderRadius: 10,
      ...getThemedShadow(theme, 'md'),     // ✅ Theme-aware shadow
    },
    btnDisabled: {
      backgroundColor: theme.muted,        // ✅ Theme color
      shadowColor: colors.gray[400],       // ✅ Theme color
    },
    label: {
      fontSize: 16,
      fontWeight: "bold",
      color: colors.white,                 // ✅ Semantic color
      letterSpacing: 0.5,
    },
  }));

  // ... rest of component logic

  return (
    <View style={[styles.wrapper, { paddingBottom: insets.bottom || 12 }]}>
      <TouchableOpacity
        style={[styles.btn, disabled && styles.btnDisabled]}
        onPress={handlePress}
        disabled={disabled}
      >
        <Text style={styles.label}>
          {getButtonText()}
        </Text>
      </TouchableOpacity>
    </View>
  );
}
```

## Migration Checklist

### ✅ Completed Steps:

1. **Import theme utilities**
   - Added `useTheme` and `useThemedStyles` imports
   - Added `getThemedShadow` utility import

2. **Replace hardcoded colors**
   - `#E5E7EB` → `theme.border`
   - `#FFFFFF` → `theme.card`
   - `rgb(168, 38, 255)` → `theme.primaryButton`
   - `#A1A1AA` → `colors.gray[400]`

3. **Use theme-aware styling**
   - Moved styles inside `useThemedStyles` hook
   - Added theme, colors, and isDark parameters
   - Used semantic color names

4. **Improve shadow handling**
   - Replaced hardcoded shadow with `getThemedShadow` utility
   - Shadow now adapts to theme

### 🧪 Testing Required:

- [ ] Test in light mode
- [ ] Test in dark mode  
- [ ] Test disabled state in both themes
- [ ] Test button press animations
- [ ] Verify accessibility contrast ratios
- [ ] Test on iOS and Android

### 📱 Visual Comparison:

**Light Mode:**
- Background: Clean white card background
- Button: Green primary color (#3da450)
- Text: White text on colored background
- Shadow: Subtle light shadow

**Dark Mode:**
- Background: Dark card background (#1e1e1e)
- Button: Same green primary (maintains brand consistency)
- Text: White text (good contrast)
- Shadow: Darker shadow for depth

## Benefits Achieved

1. **Theme Consistency**: Button now uses the same colors as the rest of the app
2. **Dark Mode Support**: Automatically works in dark mode
3. **Maintainability**: Color changes in theme system apply everywhere
4. **Accessibility**: Better contrast handling
5. **Performance**: Styles are memoized and only recreate when theme changes

## Common Pitfalls to Avoid

1. **Don't mix approaches**: Don't use both hardcoded colors and theme colors in the same component
2. **Test both themes**: Always test light and dark modes
3. **Use semantic colors**: Prefer `theme.primaryButton` over `colors.primary[600]`
4. **Consider contrast**: Ensure text is readable on all backgrounds
5. **Update animations**: Make sure color transitions work with theme changes

## Next Steps

1. Apply the same pattern to other skill player components
2. Update related components (ContinueButton, other buttons)
3. Test the entire skill player flow in both themes
4. Document any theme-specific behavior
