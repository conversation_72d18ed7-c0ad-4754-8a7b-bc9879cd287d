# Streak Tracking System - Testing Strategy

## Overview

This document outlines the comprehensive testing strategy for the streak tracking system implementation, covering backend logic, frontend components, and integration scenarios.

## Testing Scope

### 1. Backend Testing (Convex Functions)

#### **Streak Calculation Logic**

```typescript
// Test cases for streak calculation
describe('Streak Calculation', () => {
  test('should start new streak on first activity', () => {
    // User with no previous activity
    // Complete exercise -> streak = 1
  });

  test('should increment streak on consecutive days', () => {
    // User active yesterday
    // Complete exercise today -> streak = previous + 1
  });

  test('should reset streak on missed days', () => {
    // User last active 3 days ago
    // Complete exercise today -> streak = 1
  });

  test('should handle same-day multiple activities', () => {
    // User already active today
    // Complete another exercise -> streak unchanged
  });
});
```

#### **Streak Freeze Logic**

```typescript
describe('Streak Freeze', () => {
  test('should allow freeze for yesterday missed activity', () => {
    // User last active 2 days ago, has freezes
    // Use freeze -> should set lastActivity to yesterday
  });

  test('should reject freeze when no freezes available', () => {
    // User has 0 freezes
    // Attempt freeze -> should throw error
  });

  test('should reject freeze for activities older than yesterday', () => {
    // User last active 3 days ago
    // Attempt freeze -> should throw error
  });
});
```

#### **Milestone Detection**

```typescript
describe('Milestone Detection', () => {
  test('should detect 7-day milestone', () => {
    // User reaches 7-day streak
    // Should add milestone to history with celebrated: false
  });

  test('should not duplicate milestones', () => {
    // User already has 7-day milestone
    // Reach 7 days again -> should not add duplicate
  });

  test('should detect multiple milestones', () => {
    // Test 7, 30, 100, 365 day milestones
  });
});
```

### 2. Frontend Component Testing

#### **StreakDisplay Component**

```typescript
describe('StreakDisplay', () => {
  test('should show loading state when streak is loading', () => {
    // Mock loading state
    // Should render loading indicator
  });

  test('should display current streak number', () => {
    // Mock streak data with currentStreak: 5
    // Should display "5"
  });

  test('should show correct emoji for streak level', () => {
    // Test different streak levels
    // 7+ days -> 🔥, 30+ -> 💎, 100+ -> 👑, 365+ -> 🏆
  });

  test('should show notification dot for pending celebrations', () => {
    // Mock pending milestone
    // Should show red notification dot
  });

  test('should call onPress when tapped', () => {
    // Mock onPress function
    // Tap component -> should call onPress
  });
});
```

#### **StreakCelebration Component**

```typescript
describe('StreakCelebration', () => {
  test('should display correct milestone information', () => {
    // Mock 7-day milestone
    // Should show "Week Warrior!" title and 🔥 emoji
  });

  test('should animate celebration sequence', () => {
    // Test animation values change correctly
    // Scale, opacity, confetti animations
  });

  test('should mark milestone as celebrated on continue', () => {
    // Mock markMilestoneCelebrated function
    // Press continue -> should call function with milestone
  });

  test('should close modal after celebration', () => {
    // Mock onClose function
    // Complete celebration -> should call onClose
  });
});
```

#### **StreakCalendar Component**

```typescript
describe('StreakCalendar', () => {
  test('should display current month calendar', () => {
    // Should render calendar grid with correct days
  });

  test('should highlight activity days', () => {
    // Mock streak with activity history
    // Should highlight days with activity
  });

  test('should show current streak days differently', () => {
    // Mock current streak
    // Should highlight recent streak days
  });

  test('should display streak statistics', () => {
    // Should show total active days, longest streak, freezes left
  });
});
```

### 3. Integration Testing

#### **Skill Player Integration**

```typescript
describe('Skill Player Integration', () => {
  test('should update streak when exercise completed', () => {
    // Complete exercise in skill player
    // Should call streak update function
  });

  test('should update streak when lesson completed', () => {
    // Complete lesson in skill player
    // Should record lesson completion for streak
  });

  test('should handle streak update failures gracefully', () => {
    // Mock streak update failure
    // Should not break skill player flow
  });
});
```

#### **Header Integration**

```typescript
describe('Header Integration', () => {
  test('should display streak in app header', () => {
    // Mock streak data
    // Should render StreakDisplay in header
  });

  test('should open streak modal when streak tapped', () => {
    // Tap streak display
    // Should open streak calendar modal
  });

  test('should show celebration when milestone reached', () => {
    // Mock pending celebration
    // Should automatically show celebration modal
  });
});
```

### 4. Edge Case Testing

#### **Date and Timezone Handling**

```typescript
describe('Date Handling', () => {
  test('should handle timezone changes correctly', () => {
    // User travels across timezones
    // Should maintain streak based on UTC dates
  });

  test('should handle daylight saving time transitions', () => {
    // Test during DST transitions
    // Should not break streak calculations
  });

  test('should handle leap year dates', () => {
    // Test February 29th activities
    // Should handle correctly
  });
});
```

#### **Network and Offline Scenarios**

```typescript
describe('Offline Scenarios', () => {
  test('should queue streak updates when offline', () => {
    // Complete exercise while offline
    // Should queue update for when online
  });

  test('should sync queued updates when back online', () => {
    // Come back online with queued updates
    // Should sync all pending streak updates
  });

  test('should handle conflicting updates', () => {
    // Multiple devices updating same streak
    // Should resolve conflicts correctly
  });
});
```

#### **Performance Testing**

```typescript
describe('Performance', () => {
  test('should handle large streak history efficiently', () => {
    // User with 1000+ day streak
    // Should load and display quickly
  });

  test('should not block UI during streak updates', () => {
    // Streak update should be async
    // Should not freeze UI
  });

  test('should cache streak data appropriately', () => {
    // Should not refetch on every render
    // Should cache for reasonable time
  });
});
```

## Test Data Setup

### **Mock Streak Data**

```typescript
const mockStreakData = {
  newUser: {
    currentStreak: 0,
    longestStreak: 0,
    lastActivityDate: '',
    streakFreezes: 2,
    streakHistory: [],
    totalActiveDays: 0,
  },
  activeUser: {
    currentStreak: 15,
    longestStreak: 30,
    lastActivityDate: '2024-01-15',
    streakFreezes: 1,
    streakHistory: [
      { milestone: 7, achievedDate: '2024-01-08', celebrated: true },
    ],
    totalActiveDays: 45,
  },
  milestoneUser: {
    currentStreak: 30,
    longestStreak: 30,
    lastActivityDate: '2024-01-15',
    streakFreezes: 2,
    streakHistory: [
      { milestone: 7, achievedDate: '2024-01-08', celebrated: true },
      { milestone: 30, achievedDate: '2024-01-15', celebrated: false },
    ],
    totalActiveDays: 30,
  },
};
```

## Testing Tools and Setup

### **Backend Testing**

- **Convex Testing Framework**: Use Convex's built-in testing utilities
- **Jest**: For unit testing business logic
- **Test Database**: Isolated test environment

### **Frontend Testing**

- **React Native Testing Library**: Component testing
- **Jest**: Unit and integration tests
- **Detox**: End-to-end testing
- **Storybook**: Component visual testing

### **Mock Services**

```typescript
// Mock Convex client for testing
const mockConvex = {
  query: jest.fn(),
  mutation: jest.fn(),
};

// Mock streak store for component testing
const mockStreakStore = {
  streak: mockStreakData.activeUser,
  isLoading: false,
  error: null,
  updateStreak: jest.fn(),
  loadStreak: jest.fn(),
};
```

## Test Execution Strategy

### **Development Testing**

1. **Unit Tests**: Run on every code change
2. **Integration Tests**: Run on feature completion
3. **E2E Tests**: Run on major milestones

### **CI/CD Pipeline**

1. **Pre-commit**: Lint and unit tests
2. **PR Validation**: Full test suite
3. **Deployment**: E2E tests in staging

### **Manual Testing Checklist**

- [ ] Complete exercise and verify streak updates
- [ ] Test streak freeze functionality
- [ ] Verify milestone celebrations appear
- [ ] Test calendar view accuracy
- [ ] Check offline/online sync
- [ ] Validate timezone handling
- [ ] Test performance with large datasets

## Success Criteria

### **Functional Requirements**

- ✅ Streak calculation is accurate for all scenarios
- ✅ Milestone detection works correctly
- ✅ Freeze functionality operates as designed
- ✅ UI components display correct information
- ✅ Integration with skill player is seamless

### **Performance Requirements**

- ✅ Streak updates complete within 500ms
- ✅ Calendar loads within 1 second
- ✅ No memory leaks in long-running sessions
- ✅ Offline queue handles 100+ pending updates

### **Reliability Requirements**

- ✅ 99.9% uptime for streak tracking
- ✅ Zero data loss during network issues
- ✅ Graceful degradation when backend unavailable
- ✅ Consistent behavior across all platforms

This comprehensive testing strategy ensures the streak tracking system is robust, reliable, and provides an excellent user experience across all scenarios.
