# Theme System Migration Plan

## Phase 1: Color System Unification

### 1.1 Update Tailwind Configuration

```javascript
// tailwind.config.js - UPDATED
module.exports = {
  content: ['./src/**/*.{js,jsx,ts,tsx}'],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      colors: {
        // Align with theme system
        primary: {
          50: 'var(--color-primary-50)',
          100: 'var(--color-primary-100)',
          200: 'var(--color-primary-200)',
          300: 'var(--color-primary-300)',
          400: 'var(--color-primary-400)',
          500: 'var(--color-primary-500)',
          600: 'var(--color-primary-600)',
          700: 'var(--color-primary-700)',
          800: 'var(--color-primary-800)',
          900: 'var(--color-primary-900)',
          950: 'var(--color-primary-950)',
        },
        secondary: {
          50: 'var(--color-secondary-50)',
          // ... full scale
        },
        success: {
          50: 'var(--color-success-50)',
          // ... full scale
        },
        error: {
          50: 'var(--color-error-50)',
          // ... full scale
        },
        warning: {
          50: 'var(--color-warning-50)',
          // ... full scale
        },
        gray: {
          50: 'var(--color-gray-50)',
          // ... full scale
        },
        // Theme semantic colors
        background: 'var(--color-background)',
        foreground: 'var(--color-text)',
        card: 'var(--color-card)',
        'card-foreground': 'var(--color-text)',
        border: 'var(--color-border)',
        input: 'var(--color-input-background)',
        ring: 'var(--color-primary-500)',
        'destructive': 'var(--color-error-500)',
        'destructive-foreground': 'var(--color-error-50)',
        muted: 'var(--color-muted)',
        'muted-foreground': 'var(--color-secondary-text)',
        accent: 'var(--color-accent)',
        'accent-foreground': 'var(--color-text)',
      },
      fontFamily: {
        Poppins_400Regular: ['Poppins_400Regular'],
        Poppins_500Medium: ['Poppins_500Medium'],
        Poppins_600SemiBold: ['Poppins_600SemiBold'],
        Poppins_700Bold: ['Poppins_700Bold'],
      },
    },
  },
  plugins: [],
};
```

### 1.2 Create CSS Variables Bridge

```css
/* src/app/global.css - UPDATED */
@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  /* Primary Colors */
  --color-primary-50: #f0f9f1;
  --color-primary-100: #dcf3df;
  --color-primary-200: #b9e6c0;
  --color-primary-300: #8ed59a;
  --color-primary-400: #5abe70;
  --color-primary-500: #3da450;
  --color-primary-600: #2a833c;
  --color-primary-700: #236833;
  --color-primary-800: #1d512a;
  --color-primary-900: #184324;
  --color-primary-950: #0c2515;

  /* Secondary Colors */
  --color-secondary-50: #f8f6f1;
  --color-secondary-100: #efe9e1;
  --color-secondary-200: #ded0c3;
  --color-secondary-300: #cbb39e;
  --color-secondary-400: #b79274;
  --color-secondary-500: #a77b59;
  --color-secondary-600: #95674a;
  --color-secondary-700: #7c533e;
  --color-secondary-800: #664536;
  --color-secondary-900: #543a30;
  --color-secondary-950: #2d1e19;

  /* Success Colors */
  --color-success-50: #ecfdf5;
  --color-success-100: #d1fae5;
  --color-success-200: #a7f3d0;
  --color-success-300: #6ee7b7;
  --color-success-400: #34d399;
  --color-success-500: #10b981;
  --color-success-600: #059669;
  --color-success-700: #047857;
  --color-success-800: #065f46;
  --color-success-900: #064e3b;
  --color-success-950: #022c22;

  /* Error Colors */
  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;
  --color-error-950: #450a0a;

  /* Warning Colors */
  --color-warning-50: #fff9ec;
  --color-warning-100: #ffeac2;
  --color-warning-200: #fed58b;
  --color-warning-300: #fec554;
  --color-warning-400: #fdb022;
  --color-warning-500: #f79009;
  --color-warning-600: #dc6803;
  --color-warning-700: #b54708;
  --color-warning-800: #923a0e;
  --color-warning-900: #78310f;
  --color-warning-950: #451a03;

  /* Gray Colors */
  --color-gray-50: #f9fafb;
  --color-gray-100: #f3f4f6;
  --color-gray-200: #e5e7eb;
  --color-gray-300: #d1d5db;
  --color-gray-400: #9ca3af;
  --color-gray-500: #6b7280;
  --color-gray-600: #4b5563;
  --color-gray-700: #374151;
  --color-gray-800: #1f2937;
  --color-gray-900: #111827;
  --color-gray-950: #030712;

  /* Light Theme Semantic Colors */
  --color-background: #f9faf8;
  --color-text: #111827;
  --color-secondary-text: #4b5563;
  --color-card: #ffffff;
  --color-border: #dcf3df;
  --color-input-background: #ffffff;
  --color-muted: #f0f9f1;
  --color-accent: #3da450;
}

[data-theme="dark"] {
  /* Dark Theme Semantic Colors */
  --color-background: #121212;
  --color-text: #f9fafb;
  --color-secondary-text: #9ca3af;
  --color-card: #1e1e1e;
  --color-border: #2c2c2c;
  --color-input-background: #333333;
  --color-muted: #282828;
  --color-accent: #3da450;
}
```

## Phase 2: Theme Infrastructure Enhancement

### 2.1 Enhanced Theme Context

```typescript
// lib/theme.tsx - ENHANCED
import React, { createContext, useContext, useState, useEffect } from 'react';
import { useColorScheme as useNativeColorScheme } from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { colors, lightTheme, darkTheme, AppTheme } from '~/constants/colors';

type ThemeMode = 'light' | 'dark' | 'system';

type ThemeContextType = {
  isDarkMode: boolean;
  theme: AppTheme;
  colors: typeof colors;
  themeMode: ThemeMode;
  toggleTheme: () => void;
  setTheme: (mode: ThemeMode) => void;
  isLoading: boolean;
};

const ThemeContext = createContext<ThemeContextType>({
  isDarkMode: false,
  theme: lightTheme,
  colors,
  themeMode: 'system',
  toggleTheme: () => {},
  setTheme: () => {},
  isLoading: true,
});

const THEME_STORAGE_KEY = '@app_theme_mode';

export const ThemeProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const systemColorScheme = useNativeColorScheme();
  const [themeMode, setThemeMode] = useState<ThemeMode>('system');
  const [isDarkMode, setIsDarkMode] = useState(systemColorScheme === 'dark');
  const [isLoading, setIsLoading] = useState(true);

  // Load saved theme preference
  useEffect(() => {
    const loadThemePreference = async () => {
      try {
        const savedTheme = await AsyncStorage.getItem(THEME_STORAGE_KEY);
        if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
          setThemeMode(savedTheme as ThemeMode);
        }
      } catch (error) {
        console.warn('Failed to load theme preference:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadThemePreference();
  }, []);

  // Update dark mode based on theme mode and system preference
  useEffect(() => {
    if (themeMode === 'system') {
      setIsDarkMode(systemColorScheme === 'dark');
    } else {
      setIsDarkMode(themeMode === 'dark');
    }
  }, [systemColorScheme, themeMode]);

  // Update CSS variables when theme changes
  useEffect(() => {
    if (typeof document !== 'undefined') {
      document.documentElement.setAttribute(
        'data-theme',
        isDarkMode ? 'dark' : 'light'
      );
    }
  }, [isDarkMode]);

  const setTheme = async (mode: ThemeMode) => {
    setThemeMode(mode);
    try {
      await AsyncStorage.setItem(THEME_STORAGE_KEY, mode);
    } catch (error) {
      console.warn('Failed to save theme preference:', error);
    }
  };

  const toggleTheme = () => {
    const newMode = isDarkMode ? 'light' : 'dark';
    setTheme(newMode);
  };

  const theme = isDarkMode ? darkTheme : lightTheme;

  const value = {
    isDarkMode,
    theme,
    colors,
    themeMode,
    toggleTheme,
    setTheme,
    isLoading,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

export const useTheme = (): ThemeContextType => useContext(ThemeContext);

// Utility hooks
export const useThemedStyles = <T extends Record<string, any>>(
  styleCreator: (theme: AppTheme, colors: typeof colors, isDark: boolean) => T
): T => {
  const { theme, colors, isDarkMode } = useTheme();
  return styleCreator(theme, colors, isDarkMode);
};
```

### 2.2 Theme-Aware Utility Functions

```typescript
// lib/themeUtils.ts - NEW FILE
import { colors, AppTheme } from '~/constants/colors';

export const getContrastColor = (backgroundColor: string): string => {
  // Simple contrast calculation - can be enhanced
  const hex = backgroundColor.replace('#', '');
  const r = parseInt(hex.substr(0, 2), 16);
  const g = parseInt(hex.substr(2, 2), 16);
  const b = parseInt(hex.substr(4, 2), 16);
  const brightness = (r * 299 + g * 587 + b * 114) / 1000;
  return brightness > 128 ? colors.gray[900] : colors.gray[50];
};

export const getThemedColor = (
  theme: AppTheme,
  colorKey: keyof AppTheme
): string => {
  return theme[colorKey];
};

export const createThemedStyleSheet = <T extends Record<string, any>>(
  styleCreator: (theme: AppTheme, colors: typeof colors) => T
) => {
  return (theme: AppTheme, colorsObj: typeof colors): T => {
    return styleCreator(theme, colorsObj);
  };
};
```

## Phase 3: Component Migration Priority

### High Priority (Core User Experience)
1. **CheckContinueButton** - Most visible interactive element
2. **AnswerFeedback** - Critical for user feedback
3. **Tab Navigation** - Always visible
4. **MultiChoice/SingleChoice** - Core learning components

### Medium Priority (Frequent Use)
5. **Profile Screen** - User engagement
6. **Home/Index Screen** - Entry point
7. **Progress Components** - Visual feedback
8. **Onboarding Components** - First impression

### Low Priority (Less Frequent)
9. **Settings/Premium screens**
10. **Utility components**

## Phase 4: Specific Component Migrations

### 4.1 CheckContinueButton Migration

```typescript
// BEFORE (hardcoded colors)
const styles = StyleSheet.create({
  btn: {
    backgroundColor: 'rgb(168, 38, 255)', // ❌ Hardcoded
    shadowColor: 'rgb(140, 32, 212)',     // ❌ Hardcoded
  },
  btnDisabled: {
    backgroundColor: "#E5E7EB",           // ❌ Hardcoded
    shadowColor: "#A1A1AA",              // ❌ Hardcoded
  },
});

// AFTER (theme-aware)
const CheckContinueButton = ({ buttonState, onCheck, onContinue, disabled }: Props) => {
  const { theme, colors } = useTheme();

  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    btn: {
      backgroundColor: theme.primaryButton,
      shadowColor: isDark ? colors.primary[400] : colors.primary[700],
    },
    btnDisabled: {
      backgroundColor: theme.muted,
      shadowColor: colors.gray[400],
    },
    wrapper: {
      backgroundColor: theme.card,
      borderColor: theme.border,
    },
  }));

  return (
    <View style={[styles.wrapper, { paddingBottom: insets.bottom || 12 }]}>
      {/* Rest of component */}
    </View>
  );
};
```

### 4.2 AnswerFeedback Migration

```typescript
// BEFORE (hardcoded colors)
const styles = StyleSheet.create({
  correctCard: {
    backgroundColor: "#ECFDF5",  // ❌ Hardcoded
    borderColor: "#10B981",      // ❌ Hardcoded
  },
  incorrectCard: {
    backgroundColor: "#FEF2F2",  // ❌ Hardcoded
    borderColor: "#EF4444",      // ❌ Hardcoded
  },
});

// AFTER (theme-aware)
const AnswerFeedback = ({ feedback, onAnimationComplete }: Props) => {
  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    correctCard: {
      backgroundColor: colors.success[50],
      borderColor: colors.success[500],
    },
    incorrectCard: {
      backgroundColor: colors.error[50],
      borderColor: colors.error[500],
    },
    glow: {
      backgroundColor: colors.success[500],
    },
  }));

  return (
    <Animated.View style={[styles.container, animatedStyle]}>
      {/* Component content */}
    </Animated.View>
  );
};
```

### 4.3 Tab Navigation Migration

```typescript
// BEFORE (hardcoded colors)
const COLORS = {
  primary: '#000000',    // ❌ Hardcoded
  white: '#FFFFFF',      // ❌ Hardcoded
  inactive: '#888888',   // ❌ Hardcoded
};

// AFTER (theme-aware)
const CustomTabBar = ({ state, navigation }: BottomTabBarProps) => {
  const { theme, colors } = useTheme();

  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    tabBarContainer: {
      backgroundColor: theme.card,
      borderTopColor: theme.border,
      shadowColor: isDark ? colors.gray[900] : colors.gray[300],
    },
    activeIconWrapper: {
      backgroundColor: theme.muted,
    },
    tabLabel: {
      color: theme.secondaryText,
    },
    activeTabLabel: {
      color: theme.text,
    },
  }));

  return (
    <View style={styles.tabBarContainer}>
      {/* Tab items */}
    </View>
  );
};
```

## Phase 5: Dark Mode Implementation

### 5.1 Theme Toggle Component

```typescript
// components/ui/ThemeToggle.tsx - NEW FILE
import React from 'react';
import { View, TouchableOpacity, Text, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useTheme } from '~/lib/theme';

export const ThemeToggle = () => {
  const { themeMode, setTheme, isDarkMode, theme } = useTheme();

  const options = [
    { key: 'light', label: 'Light', icon: 'sunny' },
    { key: 'dark', label: 'Dark', icon: 'moon' },
    { key: 'system', label: 'System', icon: 'phone-portrait' },
  ] as const;

  const styles = useThemedStyles((theme, colors, isDark) => StyleSheet.create({
    container: {
      flexDirection: 'row',
      backgroundColor: theme.card,
      borderRadius: 12,
      padding: 4,
      borderWidth: 1,
      borderColor: theme.border,
    },
    option: {
      flex: 1,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'center',
      paddingVertical: 8,
      paddingHorizontal: 12,
      borderRadius: 8,
    },
    activeOption: {
      backgroundColor: theme.primaryButton,
    },
    optionText: {
      marginLeft: 6,
      fontSize: 14,
      fontWeight: '500',
      color: theme.secondaryText,
    },
    activeOptionText: {
      color: colors.white,
    },
  }));

  return (
    <View style={styles.container}>
      {options.map((option) => (
        <TouchableOpacity
          key={option.key}
          style={[
            styles.option,
            themeMode === option.key && styles.activeOption,
          ]}
          onPress={() => setTheme(option.key)}
        >
          <Ionicons
            name={option.icon}
            size={16}
            color={
              themeMode === option.key
                ? colors.white
                : theme.secondaryText
            }
          />
          <Text
            style={[
              styles.optionText,
              themeMode === option.key && styles.activeOptionText,
            ]}
          >
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};
```

## Phase 6: Implementation Timeline

### Week 1: Foundation
- [ ] Update Tailwind configuration
- [ ] Create CSS variables bridge
- [ ] Enhance theme context with persistence
- [ ] Create utility functions

### Week 2: Core Components
- [ ] Migrate CheckContinueButton
- [ ] Migrate AnswerFeedback
- [ ] Migrate MultiChoice/SingleChoice
- [ ] Update skill player components

### Week 3: Navigation & Screens
- [ ] Migrate tab navigation
- [ ] Update profile screen
- [ ] Update home/index screen
- [ ] Implement theme toggle UI

### Week 4: Polish & Testing
- [ ] Update remaining components
- [ ] Comprehensive testing
- [ ] Performance optimization
- [ ] Documentation updates

## Migration Checklist Template

For each component migration:

```markdown
### Component: [ComponentName]

**Before Migration:**
- [ ] Identify all hardcoded colors
- [ ] Document current visual states
- [ ] Take screenshots for comparison

**During Migration:**
- [ ] Replace hardcoded colors with theme tokens
- [ ] Test in light mode
- [ ] Test in dark mode
- [ ] Verify animations still work
- [ ] Check accessibility contrast

**After Migration:**
- [ ] Visual regression test
- [ ] Performance check
- [ ] Update component documentation
- [ ] Mark as theme-compliant
```

This plan provides a systematic approach to unifying your design system while maintaining backwards compatibility and ensuring a smooth user experience throughout the migration process.
```
